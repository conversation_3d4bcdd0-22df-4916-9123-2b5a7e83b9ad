import request from '@/utils/axios'

/**
 * 查询物流管理列表
 */
export function listLogistics(query) {
  return request({
    url: '/logistics/list',
    method: 'get',
    params: query
  })
}

/**
 * 查询物流管理详细
 */
export function getLogistics(id) {
  return request({
    url: '/logistics/' + id,
    method: 'get'
  })
}

/**
 * 新增物流管理
 */
export function addLogistics(data) {
  return request({
    url: '/logistics',
    method: 'post',
    data: data
  })
}

/**
 * 修改物流管理
 */
export function updateLogistics(data) {
  return request({
    url: '/logistics',
    method: 'put',
    data: data
  })
}

/**
 * 删除物流管理
 */
export function delLogistics(id) {
  return request({
    url: '/logistics/' + id,
    method: 'delete'
  })
}

/**
 * 导出物流管理
 */
export function exportLogistics(query) {
  return request({
    url: '/logistics/export',
    method: 'get',
    params: query
  })
}
