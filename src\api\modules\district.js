import request from '@/utils/axios'

/**
 * 获取所有省级数据
 */
export function getProvinces() {
  return request({
    url: '/district/provinces',
    method: 'get'
  })
}

/**
 * 根据省份ID获取城市列表
 */
export function getCitiesByProvinceId(provinceId) {
  return request({
    url: `/district/cities/${provinceId}`,
    method: 'get'
  })
}

/**
 * 根据城市ID获取区县列表
 */
export function getDistrictsByCityId(cityId) {
  return request({
    url: `/district/districts/${cityId}`,
    method: 'get'
  })
}

/**
 * 获取省级市级树形结构
 */
export function getDistrictTree() {
  return request({
    url: '/district/getDistrictTree',
    method: 'get'
  })
}

/**
 * 新增物流模板
 */
export function createTemplate(data) {
  return request({
    url: '/logistics/logistics',
    method: 'post',
    data: data
  })
}

/**
 * 获取运费信息
 */
export function getFreInfo(id) {
  return request({
    url: '/logistics/logistics/' + id,
    method: 'get'
  })
}

/**
 * 更新物流模板
 */
export function UpdateTemplate(data) {
  return request({
    url: '/logistics/logistics',
    method: 'put',
    data: data
  })
}
