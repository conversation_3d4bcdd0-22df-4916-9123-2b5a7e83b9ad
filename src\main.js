import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { permission, permissionAll } from '@/directives/permission'
import { initUserPermissions } from '@/utils/permission'

const app = createApp(App)

// 过滤 Element Plus 的 slot 警告
app.config.warnHandler = (msg, instance, trace) => {
  // 忽略 Element Plus 组件的 slot 警告
  if (msg.includes('Slot "default" invoked outside of the render function')) {
    return
  }
  // 其他警告正常显示
  console.warn(`[Vue warn]: ${msg}`, instance, trace)
}

// 注册权限指令
app.directive('permission', permission)
app.directive('permission-all', permissionAll)

app.use(store)
app.use(router)
app.use(ElementPlus)

// 初始化用户权限
initUserPermissions().then(() => {
  app.mount('#app')
})
