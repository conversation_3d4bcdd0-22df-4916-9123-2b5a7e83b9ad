import request from '@/utils/axios'

/**
 * 获取货区名称列表
 */
export function depotNameList() {
  return request({
    url: '/depot/nameList',
    method: 'get'
  })
}

/**
 * 查询货架列表
 */
export function listShelves(query) {
  return request({
    url: '/shelves/list',
    method: 'get',
    params: query
  })
}

/**
 * 查询货架详细
 */
export function getShelves(id) {
  return request({
    url: '/shelves/' + id,
    method: 'get'
  })
}

/**
 * 新增货架
 */
export function addShelves(data) {
  return request({
    url: '/shelves',
    method: 'post',
    data: data
  })
}

/**
 * 修改货架
 */
export function updateShelves(data) {
  return request({
    url: '/shelves',
    method: 'put',
    data: data
  })
}

/**
 * 删除货架
 */
export function delShelves(id) {
  return request({
    url: '/shelves/' + id,
    method: 'delete'
  })
}
