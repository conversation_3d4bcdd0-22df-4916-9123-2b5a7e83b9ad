import instance from '../utils/axios.js'
import { setupRequestInterceptors } from './interceptors/request'
import { setupResponseInterceptors } from './interceptors/response'

// 设置拦截器
setupRequestInterceptors(instance)
setupResponseInterceptors(instance)

// 导出所有模块接口
export { adminApi } from './modules/admin'
export { invitationApi } from './modules/invitation'
export { depotApi } from './modules/depot'
export { userApi } from './modules/user'
export { userLoginApi } from './modules/userLogin'