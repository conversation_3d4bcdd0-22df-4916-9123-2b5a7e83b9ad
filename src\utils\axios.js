import axios from 'axios'
// 创建 axios
const instance = axios.create({
	baseURL: '/api',
	timeout: 30000, // 增加超时时间到30秒
})

// 添加请求拦截器来处理FormData的Content-Type
instance.interceptors.request.use(config => {
	// 如果是FormData，让浏览器自动设置Content-Type
	if (config.data instanceof FormData) {
		// 删除Content-Type，让浏览器自动设置（包括boundary）
		delete config.headers['Content-Type']
	}
	return config
}, error => {
	return Promise.reject(error)
})

export default instance