<template>
    <el-menu router :default-active="$route.path" :collapse="false" unique-opened background-color="#304156" text-color="#bfcbd9" active-text-color="#409EFF">
        <template v-for="item in filteredMenuTree" :key="item.id">
            <!-- 有子菜单的情况 -->
            <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.path || item.id.toString()">
                <template #title>
                    <el-icon v-if="item.icon">
                        <component :is="getIcon(item.icon)" />
                    </el-icon>
                    <span>{{ item.name }}</span>
                </template>
                <el-menu-item v-for="child in item.children" :key="child.id" :index="child.path">
                    {{ child.name }}
                </el-menu-item>
            </el-sub-menu>

            <!-- 单个菜单项 -->
            <el-menu-item v-else-if="item.path" :index="item.path">
                <el-icon v-if="item.icon">
                    <component :is="getIcon(item.icon)" />
                </el-icon>
                <span>{{ item.name }}</span>
            </el-menu-item>
        </template>
    </el-menu>
</template>

<script setup lang="ts">
    import { ref, computed, onMounted } from 'vue'
    import {
        Setting, Shop, Notebook, Monitor, Document as DocIcon,
        User, Message, ShoppingCart, Connection, Box,
        TrendCharts, HomeFilled
    } from '@element-plus/icons-vue'
    import { getUserMenuTree } from '@/api/permission'
    import { hasPermission } from '@/utils/permission'

    const userMenuTree = ref([])

    // 扩展图标映射
    const iconMap = {
        'Setting': Setting,
        'Shop': Shop,
        'Notebook': Notebook,
        'Monitor': Monitor,
        'Document': DocIcon,
        'User': User,
        'Message': Message,
        'ShoppingCart': ShoppingCart,
        'Connection': Connection,
        'Box': Box,
        'TrendCharts': TrendCharts,
        'HomeFilled': HomeFilled
    }

    const getIcon = (iconName: string) => {
        return iconMap[iconName] || Setting
    }

    // 过滤菜单树，只显示有权限的菜单项
    const filteredMenuTree = computed(() => {
        return filterMenuByPermission(userMenuTree.value)
    })

    const filterMenuByPermission = (menuItems: any[]): any[] => {
        if (!menuItems || menuItems.length === 0) return []

        return menuItems.map(item => {
            // 如果有子菜单，递归过滤
            if (item.children && item.children.length > 0) {
                const filteredChildren = filterMenuByPermission(item.children)
                return {
                    ...item,
                    children: filteredChildren
                }
            }
            return item
        }).filter(item => {
            console.log('过滤菜单项:', item.name, 'type:', item.type, 'code:', item.code, 'path:', item.path)

            // 如果是目录类型（type=1），检查是否有可显示的子菜单
            if (item.type === 1) {
                const hasChildren = item.children && item.children.length > 0
                console.log('目录菜单:', item.name, '有子菜单:', hasChildren)
                return hasChildren
            }

            // 如果是菜单类型（type=2），检查权限
            if (item.type === 2) {
                // 如果有权限编码，检查权限
                if (item.code) {
                    const hasAuth = hasPermission(item.code)
                    console.log('菜单权限检查:', item.name, 'code:', item.code, '有权限:', hasAuth)
                    return hasAuth
                }
                // 如果没有权限编码，默认显示
                console.log('菜单无权限编码，默认显示:', item.name)
                return true
            }

            // 其他类型默认不显示
            console.log('其他类型菜单不显示:', item.name, 'type:', item.type)
            return false
        })
    }

    const loadUserMenu = async () => {
        try {
            const res = await getUserMenuTree()
            if (res.code === 200) {
                userMenuTree.value = res.data || []
                console.log('用户菜单树:', userMenuTree.value)
                console.log('过滤后的菜单:', filteredMenuTree.value)
            }
        } catch (error) {
            console.error('获取用户菜单失败:', error)
        }
    }

    onMounted(() => {
        loadUserMenu()
    })
</script>

<style scoped>
    .el-menu {
        height: 100%;
        border-right: none;
    }

    .el-menu-item.is-active {
        background-color: #263445 !important;
    }

    :deep(.el-menu) {
        border-right: none;
    }

    :deep(.el-sub-menu.is-opened) {
        > .el-sub-menu__title,
        .el-menu-item {
            background-color: #1a1a1a !important;
        }
    }

    :deep(.el-menu-item):hover,
    :deep(.el-sub-menu__title):hover {
        background-color: #1a1a1a !important;
    }

    :deep(.el-menu-item.is-active) {
        background-color: #1a1a1a !important;
        color: var(--el-menu-active-color);
    }

    :deep(.el-menu-item.is-active + .el-sub-menu .el-sub-menu__title),
    :deep(.el-menu-item.is-active) ~ .el-sub-menu .el-sub-menu__title {
        background-color: #1a1a1a !important;
    }

    :deep(.el-sub-menu) {
        &.is-active {
            > .el-sub-menu__title {
                background-color: #1a1a1a !important;
            }
        }
    }
</style>